namespace DocumentCreationSystem.Models.AgentOps
{
    /// <summary>
    /// 对话上下文
    /// </summary>
    public class DialogContext
    {
        public string SessionId { get; set; } = Guid.NewGuid().ToString();
        public string? UserId { get; set; }
        public string? ProjectId { get; set; }
        public List<string> ActiveWorkflows { get; set; } = new();
        public Dictionary<string, object> Variables { get; set; } = new();
        public DialogState State { get; set; } = DialogState.Ready;
        public DateTime LastActivity { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 对话状态枚举
    /// </summary>
    public enum DialogState
    {
        Ready,
        Processing,
        WaitingForInput,
        WorkflowRunning,
        Error,
        Paused
    }

    /// <summary>
    /// 工作流状态监控器
    /// </summary>
    public class WorkflowStatusMonitor
    {
        public string WorkflowId { get; set; } = string.Empty;
        public WorkflowStatus Status { get; set; }
        public int Progress { get; set; }
        public string CurrentStep { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 智能决策建议
    /// </summary>
    public class SmartDecisionSuggestion
    {
        public string DecisionId { get; set; } = Guid.NewGuid().ToString();
        public string RecommendedAction { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public string ExpectedOutcome { get; set; } = string.Empty;
        public List<OptionAnalysis> OptionAnalysis { get; set; } = new();
        public List<RiskAssessment> RiskAssessment { get; set; } = new();
        public List<string> ImplementationSteps { get; set; } = new();
        public Dictionary<string, object> ExpectedBenefits { get; set; } = new();
    }

    /// <summary>
    /// 选项分析
    /// </summary>
    public class OptionAnalysis
    {
        public string Option { get; set; } = string.Empty;
        public double Score { get; set; }
        public string Risk { get; set; } = string.Empty;
    }

    /// <summary>
    /// 风险评估
    /// </summary>
    public class RiskAssessment
    {
        public string RiskType { get; set; } = string.Empty;
        public string Level { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 决策上下文
    /// </summary>
    public class DecisionContext
    {
        public string ContextType { get; set; } = string.Empty;
        public string CurrentSituation { get; set; } = string.Empty;
        public List<string> AvailableOptions { get; set; } = new();
        public Dictionary<string, object> Constraints { get; set; } = new();
    }

    /// <summary>
    /// 工作流进度报告
    /// </summary>
    public class WorkflowProgressReport
    {
        public string WorkflowId { get; set; } = string.Empty;
        public string WorkflowName { get; set; } = string.Empty;
        public WorkflowStatus Status { get; set; }
        public int ProgressPercentage { get; set; }
        public List<StepProgress> StepProgresses { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 步骤进度
    /// </summary>
    public class StepProgress
    {
        public string StepId { get; set; } = string.Empty;
        public string StepName { get; set; } = string.Empty;
        public StepStatus Status { get; set; }
        public int ProgressPercentage { get; set; }
    }

    /// <summary>
    /// 意图分析结果
    /// </summary>
    public class IntentAnalysisResult
    {
        public string Intent { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public List<string> RecommendedActions { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 上下文帮助信息
    /// </summary>
    public class ContextualHelpInfo
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<string> Tips { get; set; } = new();
        public List<string> RelatedCommands { get; set; } = new();
    }

    /// <summary>
    /// 预测操作
    /// </summary>
    public class PredictedAction
    {
        public string ActionId { get; set; } = Guid.NewGuid().ToString();
        public string ActionName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Probability { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 集成状态概览
    /// </summary>
    public class IntegrationStatusOverview
    {
        public int TotalActiveWorkflows { get; set; }
        public HealthStatus SystemHealth { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        public PerformanceMetrics PerformanceMetrics { get; set; } = new();
        public List<RecentActivity> RecentActivities { get; set; } = new();
    }

    /// <summary>
    /// 智能决策请求
    /// </summary>
    public class SmartDecisionRequest
    {
        public string RequestId { get; set; } = Guid.NewGuid().ToString();
        public string Context { get; set; } = string.Empty;
        public List<string> Options { get; set; } = new();
        public Dictionary<string, object> Criteria { get; set; } = new();
    }

    /// <summary>
    /// 智能决策结果
    /// </summary>
    public class SmartDecisionResult
    {
        public string RequestId { get; set; } = string.Empty;
        public string RecommendedOption { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public string Reasoning { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 集成上下文
    /// </summary>
    public class IntegrationContext
    {
        public List<string> CurrentWorkflows { get; set; } = new();
        public Dictionary<string, object> UserContext { get; set; } = new();
        public string SessionId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 监控范围
    /// </summary>
    public enum MonitoringScope
    {
        System,
        Workflow,
        User,
        Project
    }

    /// <summary>
    /// 监控数据
    /// </summary>
    public class MonitoringData
    {
        public MonitoringScope Scope { get; set; }
        public SystemMetrics SystemMetrics { get; set; } = new();
        public List<WorkflowMetrics> WorkflowMetrics { get; set; } = new();
        public DateTime CollectedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 系统指标
    /// </summary>
    public class SystemMetrics
    {
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public double DiskUsage { get; set; }
        public int ActiveConnections { get; set; }
    }

    /// <summary>
    /// 工作流性能指标
    /// </summary>
    public class WorkflowPerformanceMetrics
    {
        public string WorkflowId { get; set; } = string.Empty;
        public TimeSpan AverageExecutionTime { get; set; }
        public double SuccessRate { get; set; }
        public int TotalExecutions { get; set; }
    }

    /// <summary>
    /// 系统健康状态
    /// </summary>
    public class SystemHealthStatus
    {
        public HealthStatus OverallHealth { get; set; }
        public List<ComponentHealth> Components { get; set; } = new();
        public DateTime LastChecked { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 组件健康状态
    /// </summary>
    public class ComponentHealth
    {
        public string ComponentName { get; set; } = string.Empty;
        public HealthStatus Status { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 资源使用信息
    /// </summary>
    public class ResourceUsageInfo
    {
        public double CpuUsagePercentage { get; set; }
        public long MemoryUsageBytes { get; set; }
        public long DiskUsageBytes { get; set; }
        public int NetworkConnections { get; set; }
    }

    /// <summary>
    /// 错误统计
    /// </summary>
    public class ErrorStatistics
    {
        public int TotalErrors { get; set; }
        public int CriticalErrors { get; set; }
        public int Warnings { get; set; }
        public Dictionary<string, int> ErrorsByType { get; set; } = new();
    }

    /// <summary>
    /// 用户活动分析
    /// </summary>
    public class UserActivityAnalysis
    {
        public int ActiveUsers { get; set; }
        public int TotalSessions { get; set; }
        public TimeSpan AverageSessionDuration { get; set; }
        public Dictionary<string, int> ActionCounts { get; set; } = new();
    }

    /// <summary>
    /// 质量趋势分析
    /// </summary>
    public class QualityTrendAnalysis
    {
        public double AverageQualityScore { get; set; }
        public List<QualityDataPoint> TrendData { get; set; } = new();
        public string TrendDirection { get; set; } = string.Empty;
    }

    /// <summary>
    /// 质量数据点
    /// </summary>
    public class QualityDataPoint
    {
        public DateTime Timestamp { get; set; }
        public double QualityScore { get; set; }
        public string Category { get; set; } = string.Empty;
    }

    /// <summary>
    /// 自定义指标定义
    /// </summary>
    public class CustomMetricDefinition
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public Dictionary<string, object> Configuration { get; set; } = new();
    }

    /// <summary>
    /// 告警配置
    /// </summary>
    public class AlertConfiguration
    {
        public string Name { get; set; } = string.Empty;
        public string Condition { get; set; } = string.Empty;
        public AlertLevel Level { get; set; }
        public List<string> NotificationChannels { get; set; } = new();
    }

    /// <summary>
    /// 告警历史
    /// </summary>
    public class AlertHistory
    {
        public string AlertId { get; set; } = Guid.NewGuid().ToString();
        public string AlertName { get; set; } = string.Empty;
        public AlertLevel Level { get; set; }
        public DateTime TriggeredAt { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 报告配置
    /// </summary>
    public class ReportConfiguration
    {
        public string ReportType { get; set; } = string.Empty;
        public TimeRange TimeRange { get; set; } = new();
        public List<string> IncludedMetrics { get; set; } = new();
        public string Format { get; set; } = "PDF";
    }

    /// <summary>
    /// 监控报告
    /// </summary>
    public class MonitoringReport
    {
        public string ReportId { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
        public string Content { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
    }

    /// <summary>
    /// 数据导出配置
    /// </summary>
    public class DataExportConfiguration
    {
        public string ExportType { get; set; } = string.Empty;
        public TimeRange TimeRange { get; set; } = new();
        public string Format { get; set; } = "CSV";
        public List<string> IncludedFields { get; set; } = new();
    }

    /// <summary>
    /// 导出结果
    /// </summary>
    public class ExportResult
    {
        public string ExportId { get; set; } = Guid.NewGuid().ToString();
        public string FilePath { get; set; } = string.Empty;
        public long FileSizeBytes { get; set; }
        public DateTime ExportedAt { get; set; } = DateTime.Now;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 健康状态枚举
    /// </summary>
    public enum HealthStatus
    {
        Healthy,
        Warning,
        Critical,
        Unknown
    }

    /// <summary>
    /// 告警级别枚举
    /// </summary>
    public enum AlertLevel
    {
        Info,
        Warning,
        Error,
        Critical
    }

    /// <summary>
    /// 时间范围
    /// </summary>
    public class TimeRange
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
    }

    /// <summary>
    /// 最近活动
    /// </summary>
    public class RecentActivity
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public ActivityType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public ActivitySeverity Severity { get; set; } = ActivitySeverity.Info;
    }

    /// <summary>
    /// 活动类型枚举
    /// </summary>
    public enum ActivityType
    {
        WorkflowStarted,
        WorkflowCompleted,
        WorkflowFailed,
        UserLogin,
        UserLogout,
        SystemError
    }

    /// <summary>
    /// 活动严重程度枚举
    /// </summary>
    public enum ActivitySeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    /// <summary>
    /// 模板元数据
    /// </summary>
    public class TemplateMetadata
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0.0";
        public string Author { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        public List<string> Tags { get; set; } = new();
        public string Category { get; set; } = string.Empty;
        public TemplateType Type { get; set; } = TemplateType.General;
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 模板配置
    /// </summary>
    public class TemplateConfiguration
    {
        public string TemplateId { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public ExecutionSettings ExecutionSettings { get; set; } = new();
        public ValidationRules ValidationRules { get; set; } = new();
        public OutputSettings OutputSettings { get; set; } = new();
        public bool IsEnabled { get; set; } = true;
        public int Priority { get; set; } = 5;
        public DateTime ConfiguredAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 模板类型枚举
    /// </summary>
    public enum TemplateType
    {
        General,
        Character,
        Workflow,
        Content,
        Analysis,
        Report
    }

    /// <summary>
    /// 执行设置
    /// </summary>
    public class ExecutionSettings
    {
        public int MaxRetries { get; set; } = 3;
        public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(10);
        public bool EnableParallelExecution { get; set; } = false;
        public int MaxConcurrency { get; set; } = 1;
        public Dictionary<string, object> CustomSettings { get; set; } = new();
    }

    /// <summary>
    /// 验证规则
    /// </summary>
    public class ValidationRules
    {
        public List<string> RequiredParameters { get; set; } = new();
        public Dictionary<string, object> ParameterConstraints { get; set; } = new();
        public List<string> ValidationScripts { get; set; } = new();
        public bool StrictMode { get; set; } = false;
    }

    /// <summary>
    /// 输出设置
    /// </summary>
    public class OutputSettings
    {
        public string Format { get; set; } = "json";
        public string OutputPath { get; set; } = string.Empty;
        public bool IncludeMetadata { get; set; } = true;
        public bool CompressOutput { get; set; } = false;
        public Dictionary<string, object> FormatOptions { get; set; } = new();
    }

    /// <summary>
    /// 工作流状态枚举
    /// </summary>
    public enum WorkflowStatus
    {
        NotStarted,
        Running,
        Paused,
        Completed,
        Failed,
        Cancelled
    }

    /// <summary>
    /// 步骤状态枚举
    /// </summary>
    public enum StepStatus
    {
        Pending,
        Running,
        Completed,
        Failed,
        Skipped
    }

    /// <summary>
    /// 工作流指标
    /// </summary>
    public class WorkflowMetrics
    {
        public string WorkflowId { get; set; } = string.Empty;
        public string WorkflowName { get; set; } = string.Empty;
        public TimeSpan ExecutionTime { get; set; }
        public int StepCount { get; set; }
        public int CompletedSteps { get; set; }
        public int FailedSteps { get; set; }
        public double SuccessRate { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public Dictionary<string, object> CustomMetrics { get; set; } = new();
    }
